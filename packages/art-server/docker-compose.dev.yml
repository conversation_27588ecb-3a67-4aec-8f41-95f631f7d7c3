version: '3.8'

services:
  art-server-dev:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: art-server-dev
    ports:
      - "3000:3000"
      - "9229:9229"  # Debug port
    environment:
      - NODE_ENV=development
      - PORT=3000
      - DATABASE_URL=********************************************/artdb?schema=public
    volumes:
      - .:/app
      - /app/node_modules
      - uploads_dev:/app/uploads
    depends_on:
      - postgres
      - redis
    restart: unless-stopped
    networks:
      - art-network-dev
    command: pnpm run start:debug

  postgres:
    image: postgres:15-alpine
    container_name: art-postgres-dev
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
      - POSTGRES_DB=artdb
    volumes:
      - postgres_data_dev:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    restart: unless-stopped
    networks:
      - art-network-dev

  redis:
    image: redis:7-alpine
    container_name: art-redis-dev
    ports:
      - "6379:6379"
    volumes:
      - redis_data_dev:/data
    restart: unless-stopped
    networks:
      - art-network-dev
    command: redis-server --appendonly yes

  adminer:
    image: adminer
    container_name: art-adminer-dev
    ports:
      - "8080:8080"
    depends_on:
      - postgres
    restart: unless-stopped
    networks:
      - art-network-dev

volumes:
  postgres_data_dev:
  redis_data_dev:
  uploads_dev:

networks:
  art-network-dev:
    driver: bridge
