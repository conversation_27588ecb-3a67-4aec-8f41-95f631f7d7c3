<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文件上传测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .upload-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 2px dashed #ddd;
            border-radius: 8px;
            text-align: center;
        }
        .upload-section h3 {
            margin-top: 0;
            color: #333;
        }
        input[type="file"] {
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            width: 100%;
            max-width: 400px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>文件上传测试页面</h1>
        
        <!-- 单文件上传 -->
        <div class="upload-section">
            <h3>单文件上传</h3>
            <input type="file" id="singleFile" />
            <br>
            <button onclick="uploadSingle()">上传文件</button>
            <div id="singleResult" class="result" style="display: none;"></div>
        </div>

        <!-- 多文件上传 -->
        <div class="upload-section">
            <h3>多文件上传</h3>
            <input type="file" id="multipleFiles" multiple />
            <br>
            <button onclick="uploadMultiple()">上传多个文件</button>
            <div id="multipleResult" class="result" style="display: none;"></div>
        </div>

        <!-- 图片上传 -->
        <div class="upload-section">
            <h3>图片上传（专用接口）</h3>
            <input type="file" id="imageFile" accept="image/*" />
            <br>
            <button onclick="uploadImage()">上传图片</button>
            <div id="imageResult" class="result" style="display: none;"></div>
        </div>

        <!-- 文档上传 -->
        <div class="upload-section">
            <h3>文档上传（专用接口）</h3>
            <input type="file" id="documentFile" accept=".pdf,.doc,.docx,.txt" />
            <br>
            <button onclick="uploadDocument()">上传文档</button>
            <div id="documentResult" class="result" style="display: none;"></div>
        </div>

        <!-- 文件列表 -->
        <div class="upload-section">
            <h3>文件列表</h3>
            <button onclick="getFileList()">获取文件列表</button>
            <div id="listResult" class="result" style="display: none;"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3000';

        function showResult(elementId, data, isError = false) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = `result ${isError ? 'error' : 'success'}`;
            element.textContent = JSON.stringify(data, null, 2);
        }

        async function uploadSingle() {
            const fileInput = document.getElementById('singleFile');
            const file = fileInput.files[0];
            
            if (!file) {
                showResult('singleResult', { error: '请选择文件' }, true);
                return;
            }

            const formData = new FormData();
            formData.append('file', file);

            try {
                const response = await fetch(`${API_BASE}/upload/single`, {
                    method: 'POST',
                    body: formData
                });
                const result = await response.json();
                showResult('singleResult', result, !response.ok);
            } catch (error) {
                showResult('singleResult', { error: error.message }, true);
            }
        }

        async function uploadMultiple() {
            const fileInput = document.getElementById('multipleFiles');
            const files = fileInput.files;
            
            if (files.length === 0) {
                showResult('multipleResult', { error: '请选择文件' }, true);
                return;
            }

            const formData = new FormData();
            for (let i = 0; i < files.length; i++) {
                formData.append('files', files[i]);
            }

            try {
                const response = await fetch(`${API_BASE}/upload/multiple`, {
                    method: 'POST',
                    body: formData
                });
                const result = await response.json();
                showResult('multipleResult', result, !response.ok);
            } catch (error) {
                showResult('multipleResult', { error: error.message }, true);
            }
        }

        async function uploadImage() {
            const fileInput = document.getElementById('imageFile');
            const file = fileInput.files[0];
            
            if (!file) {
                showResult('imageResult', { error: '请选择图片文件' }, true);
                return;
            }

            const formData = new FormData();
            formData.append('image', file);

            try {
                const response = await fetch(`${API_BASE}/upload/image`, {
                    method: 'POST',
                    body: formData
                });
                const result = await response.json();
                showResult('imageResult', result, !response.ok);
            } catch (error) {
                showResult('imageResult', { error: error.message }, true);
            }
        }

        async function uploadDocument() {
            const fileInput = document.getElementById('documentFile');
            const file = fileInput.files[0];
            
            if (!file) {
                showResult('documentResult', { error: '请选择文档文件' }, true);
                return;
            }

            const formData = new FormData();
            formData.append('document', file);

            try {
                const response = await fetch(`${API_BASE}/upload/document`, {
                    method: 'POST',
                    body: formData
                });
                const result = await response.json();
                showResult('documentResult', result, !response.ok);
            } catch (error) {
                showResult('documentResult', { error: error.message }, true);
            }
        }

        async function getFileList() {
            try {
                const response = await fetch(`${API_BASE}/upload/list`);
                const result = await response.json();
                showResult('listResult', result, !response.ok);
            } catch (error) {
                showResult('listResult', { error: error.message }, true);
            }
        }
    </script>
</body>
</html>
