# 文件上传 API 文档

## 概述

这是一个基于 NestJS 和 Multer 的文件上传模块，支持单文件上传、多文件上传、按类型上传等功能。

## 功能特性

- ✅ 单文件上传
- ✅ 多文件上传
- ✅ 按文件类型分类存储
- ✅ 文件类型验证
- ✅ 文件大小限制
- ✅ 自动生成唯一文件名
- ✅ 静态文件访问
- ✅ 文件管理接口

## 支持的文件类型

### 图片文件
- 扩展名：jpg, jpeg, png, gif, webp, svg
- 大小限制：5MB

### 文档文件
- 扩展名：pdf, doc, docx, txt, rtf
- 大小限制：10MB

### 压缩文件
- 扩展名：zip, rar, 7z, tar, gz
- 大小限制：50MB

### 视频文件
- 扩展名：mp4, avi, mov, wmv, flv, webm
- 大小限制：100MB

### 音频文件
- 扩展名：mp3, wav, flac, aac, ogg
- 大小限制：20MB

## API 接口

### 1. 单文件上传

**接口地址：** `POST /upload/single`

**请求参数：**
- `file`: 文件（form-data）

**响应示例：**
```json
{
  "success": true,
  "message": "文件上传成功",
  "data": {
    "id": "file_1703123456789_abc123",
    "originalName": "example.jpg",
    "fileName": "1703123456789-xyz789.jpg",
    "filePath": "/path/to/uploads/image/1703123456789-xyz789.jpg",
    "fileUrl": "http://localhost:3000/uploads/image/1703123456789-xyz789.jpg",
    "size": 1024000,
    "mimeType": "image/jpeg",
    "fileType": "image",
    "uploadTime": "2023-12-21T10:30:56.789Z"
  }
}
```

### 2. 多文件上传

**接口地址：** `POST /upload/multiple`

**请求参数：**
- `files`: 文件数组（form-data，最多10个文件）

**响应示例：**
```json
{
  "success": true,
  "message": "文件上传完成",
  "data": {
    "success": [
      {
        "id": "file_1703123456789_abc123",
        "originalName": "file1.jpg",
        "fileName": "1703123456789-xyz789.jpg",
        "filePath": "/path/to/uploads/image/1703123456789-xyz789.jpg",
        "fileUrl": "http://localhost:3000/uploads/image/1703123456789-xyz789.jpg",
        "size": 1024000,
        "mimeType": "image/jpeg",
        "fileType": "image",
        "uploadTime": "2023-12-21T10:30:56.789Z"
      }
    ],
    "failed": [],
    "total": 1,
    "successCount": 1,
    "failedCount": 0
  }
}
```

### 3. 图片上传（专用接口）

**接口地址：** `POST /upload/image`

**请求参数：**
- `image`: 图片文件（form-data）

**说明：** 只接受图片格式文件，其他格式会被拒绝。

### 4. 文档上传（专用接口）

**接口地址：** `POST /upload/document`

**请求参数：**
- `document`: 文档文件（form-data）

**说明：** 只接受文档格式文件，其他格式会被拒绝。

### 5. 获取文件列表

**接口地址：** `GET /upload/list`

**查询参数：**
- `page`: 页码（可选，默认1）
- `limit`: 每页数量（可选，默认10）
- `fileType`: 文件类型筛选（可选）
- `search`: 文件名搜索（可选）
- `startDate`: 开始时间（可选）
- `endDate`: 结束时间（可选）

### 6. 获取文件信息

**接口地址：** `GET /upload/:fileId`

**路径参数：**
- `fileId`: 文件ID

### 7. 删除单个文件

**接口地址：** `DELETE /upload/:fileId`

**路径参数：**
- `fileId`: 文件ID

### 8. 批量删除文件

**接口地址：** `DELETE /upload`

**请求体：**
```json
{
  "fileIds": ["file_id_1", "file_id_2"]
}
```

## 使用示例

### JavaScript/TypeScript

```javascript
// 单文件上传
const uploadSingle = async (file) => {
  const formData = new FormData();
  formData.append('file', file);
  
  const response = await fetch('/upload/single', {
    method: 'POST',
    body: formData
  });
  
  return await response.json();
};

// 多文件上传
const uploadMultiple = async (files) => {
  const formData = new FormData();
  files.forEach(file => {
    formData.append('files', file);
  });
  
  const response = await fetch('/upload/multiple', {
    method: 'POST',
    body: formData
  });
  
  return await response.json();
};
```

### cURL

```bash
# 单文件上传
curl -X POST \
  http://localhost:3000/upload/single \
  -F "file=@/path/to/your/file.jpg"

# 多文件上传
curl -X POST \
  http://localhost:3000/upload/multiple \
  -F "files=@/path/to/file1.jpg" \
  -F "files=@/path/to/file2.pdf"
```

## 测试

1. 启动服务器：
```bash
npm run start:dev
```

2. 访问测试页面：
```
http://localhost:3000/upload-test.html
```

## 注意事项

1. 文件会按类型自动分类存储在不同的子目录中
2. 文件名会自动生成唯一标识，避免重名冲突
3. 上传的文件可以通过 `/uploads/` 路径直接访问
4. 建议在生产环境中配置适当的文件大小限制和安全策略
5. 当前版本的文件列表和删除功能需要配合数据库使用，请根据实际需求实现数据持久化逻辑

## 配置说明

可以通过修改 `upload.config.ts` 文件来调整：
- 支持的文件类型
- 文件大小限制
- 存储路径
- 文件命名规则
