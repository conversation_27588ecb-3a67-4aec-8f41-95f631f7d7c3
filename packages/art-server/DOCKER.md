# Art Server Docker 部署指南

本文档介绍如何使用Docker部署Art Server应用。

## 📋 前置要求

- Docker 20.10+
- Docker Compose 2.0+
- 至少2GB可用内存
- 至少5GB可用磁盘空间

## 🚀 快速开始

### 1. 克隆项目并进入目录

```bash
cd packages/art-server
```

### 2. 复制环境变量文件

```bash
cp .env.example .env
```

编辑 `.env` 文件，根据需要修改配置。

### 3. 生产环境部署

```bash
# 使用脚本部署
chmod +x scripts/deploy.sh
./scripts/deploy.sh

# 或者手动部署
docker-compose up -d
```

### 4. 开发环境部署

```bash
# 使用脚本部署
./scripts/deploy.sh dev

# 或者手动部署
docker-compose -f docker-compose.dev.yml up -d
```

## 📦 服务组件

### 生产环境 (docker-compose.yml)

- **art-server**: 主应用服务
- **postgres**: PostgreSQL数据库
- **redis**: Redis缓存
- **nginx**: 反向代理和静态文件服务

### 开发环境 (docker-compose.dev.yml)

- **art-server-dev**: 开发模式的主应用（支持热重载和调试）
- **postgres**: PostgreSQL数据库
- **redis**: Redis缓存
- **adminer**: 数据库管理界面

## 🔧 构建镜像

### 手动构建

```bash
# 生产镜像
docker build -t art-server .

# 开发镜像
docker build -f Dockerfile.dev -t art-server:dev .
```

### 使用构建脚本

```bash
chmod +x scripts/build.sh

# 构建本地镜像
./scripts/build.sh

# 构建并推送到registry
./scripts/build.sh v1.0.0 your-registry.com
```

## 🌐 访问地址

### 生产环境

- 应用主页: http://localhost
- API文档: http://localhost/api-docs
- 健康检查: http://localhost/health

### 开发环境

- 应用主页: http://localhost:3000
- API文档: http://localhost:3000/api-docs
- 健康检查: http://localhost:3000/health
- 数据库管理: http://localhost:8080

## 📊 监控和日志

### 查看服务状态

```bash
docker-compose ps
```

### 查看日志

```bash
# 查看所有服务日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f art-server
```

### 健康检查

```bash
curl http://localhost:3000/health
```

## 🔧 常用命令

### 启动服务

```bash
docker-compose up -d
```

### 停止服务

```bash
docker-compose down
```

### 重启服务

```bash
docker-compose restart
```

### 更新服务

```bash
docker-compose pull
docker-compose up -d
```

### 清理资源

```bash
# 停止并删除容器、网络
docker-compose down

# 同时删除卷（注意：会丢失数据）
docker-compose down -v

# 清理未使用的镜像
docker image prune -f
```

## 🗄️ 数据持久化

以下数据会持久化存储：

- **postgres_data**: PostgreSQL数据库数据
- **redis_data**: Redis缓存数据
- **uploads**: 用户上传的文件

## 🔒 安全配置

### 生产环境安全建议

1. **修改默认密码**: 更改数据库和Redis的默认密码
2. **使用HTTPS**: 配置SSL证书
3. **限制网络访问**: 使用防火墙限制端口访问
4. **定期备份**: 设置数据库和文件的定期备份
5. **监控日志**: 设置日志监控和告警

### 环境变量安全

```bash
# 生产环境不要使用默认值
DATABASE_URL=***********************************************/artdb
JWT_SECRET=your-very-strong-jwt-secret-key
```

## 🐛 故障排除

### 常见问题

1. **端口冲突**
   ```bash
   # 检查端口占用
   lsof -i :3000
   
   # 修改docker-compose.yml中的端口映射
   ports:
     - "3001:3000"  # 改为其他端口
   ```

2. **内存不足**
   ```bash
   # 检查Docker内存使用
   docker stats
   
   # 增加Docker内存限制或清理未使用的容器
   docker system prune -f
   ```

3. **数据库连接失败**
   ```bash
   # 检查数据库容器状态
   docker-compose logs postgres
   
   # 重启数据库服务
   docker-compose restart postgres
   ```

### 调试模式

开发环境支持Node.js调试：

```bash
# 启动开发环境
docker-compose -f docker-compose.dev.yml up -d

# 连接调试器到端口9229
```

## 📈 性能优化

### 生产环境优化

1. **启用Nginx缓存**
2. **配置Gzip压缩**
3. **使用CDN加速静态资源**
4. **数据库连接池优化**
5. **Redis缓存策略优化**

### 资源限制

在docker-compose.yml中添加资源限制：

```yaml
services:
  art-server:
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'
```

## 🔄 更新和维护

### 应用更新

```bash
# 拉取最新代码
git pull

# 重新构建和部署
./scripts/deploy.sh
```

### 数据库迁移

```bash
# 进入应用容器
docker-compose exec art-server sh

# 运行数据库迁移
npx prisma migrate deploy
```

## 📞 支持

如果遇到问题，请：

1. 检查日志: `docker-compose logs -f`
2. 查看服务状态: `docker-compose ps`
3. 检查健康状态: `curl http://localhost:3000/health`
4. 提交Issue到项目仓库
