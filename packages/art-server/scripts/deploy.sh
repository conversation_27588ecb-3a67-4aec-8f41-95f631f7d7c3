#!/bin/bash

# 部署脚本
set -e

echo "🚀 开始部署 Art Server..."

# 检查是否在正确的目录
if [ ! -f "docker-compose.yml" ]; then
    echo "❌ 错误: 请在 art-server 目录下运行此脚本"
    exit 1
fi

# 设置变量
ENVIRONMENT=${1:-production}
COMPOSE_FILE="docker-compose.yml"

if [ "$ENVIRONMENT" = "dev" ] || [ "$ENVIRONMENT" = "development" ]; then
    COMPOSE_FILE="docker-compose.dev.yml"
    echo "🔧 使用开发环境配置"
else
    echo "🏭 使用生产环境配置"
fi

# 停止现有容器
echo "🛑 停止现有容器..."
docker-compose -f "$COMPOSE_FILE" down

# 拉取最新镜像（如果使用远程镜像）
echo "📥 拉取最新镜像..."
docker-compose -f "$COMPOSE_FILE" pull || true

# 构建镜像（如果需要）
echo "🔨 构建镜像..."
docker-compose -f "$COMPOSE_FILE" build

# 启动服务
echo "▶️ 启动服务..."
docker-compose -f "$COMPOSE_FILE" up -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 10

# 检查服务状态
echo "🔍 检查服务状态..."
docker-compose -f "$COMPOSE_FILE" ps

# 健康检查
echo "🏥 执行健康检查..."
if curl -f http://localhost:3000/health > /dev/null 2>&1; then
    echo "✅ 服务健康检查通过"
else
    echo "⚠️ 服务健康检查失败，请检查日志"
    docker-compose -f "$COMPOSE_FILE" logs art-server
fi

echo "🎉 部署完成!"
echo ""
echo "服务访问地址:"
echo "- 应用: http://localhost:3000"
echo "- API文档: http://localhost:3000/api-docs"
echo "- 健康检查: http://localhost:3000/health"

if [ "$ENVIRONMENT" = "dev" ] || [ "$ENVIRONMENT" = "development" ]; then
    echo "- 数据库管理: http://localhost:8080"
fi

echo ""
echo "查看日志: docker-compose -f $COMPOSE_FILE logs -f"
echo "停止服务: docker-compose -f $COMPOSE_FILE down"
