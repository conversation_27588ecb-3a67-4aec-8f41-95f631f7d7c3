#!/bin/bash

# 构建脚本
set -e

echo "🚀 开始构建 Art Server Docker 镜像..."

# 检查是否在正确的目录
if [ ! -f "package.json" ]; then
    echo "❌ 错误: 请在 art-server 目录下运行此脚本"
    exit 1
fi

# 设置变量
IMAGE_NAME="art-server"
VERSION=${1:-latest}
REGISTRY=${2:-""}

if [ ! -z "$REGISTRY" ]; then
    FULL_IMAGE_NAME="$REGISTRY/$IMAGE_NAME:$VERSION"
else
    FULL_IMAGE_NAME="$IMAGE_NAME:$VERSION"
fi

echo "📦 构建镜像: $FULL_IMAGE_NAME"

# 构建Docker镜像
docker build -t "$FULL_IMAGE_NAME" .

echo "✅ 镜像构建完成: $FULL_IMAGE_NAME"

# 如果提供了registry，推送镜像
if [ ! -z "$REGISTRY" ]; then
    echo "📤 推送镜像到 registry..."
    docker push "$FULL_IMAGE_NAME"
    echo "✅ 镜像推送完成"
fi

echo "🎉 构建流程完成!"
echo ""
echo "使用以下命令运行容器:"
echo "docker run -p 3000:3000 $FULL_IMAGE_NAME"
echo ""
echo "或者使用 docker-compose:"
echo "docker-compose up -d"
