version: '3.8'

services:
  art-server:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: art-server
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - PORT=3000
      - DATABASE_URL=********************************************/artdb?schema=public
    volumes:
      - uploads:/app/uploads
      - ./public:/app/public:ro
    depends_on:
      - postgres
      - redis
    restart: unless-stopped
    networks:
      - art-network

  postgres:
    image: postgres:15-alpine
    container_name: art-postgres
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
      - POSTGRES_DB=artdb
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./prisma/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    ports:
      - "5432:5432"
    restart: unless-stopped
    networks:
      - art-network

  redis:
    image: redis:7-alpine
    container_name: art-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - art-network
    command: redis-server --appendonly yes

  nginx:
    image: nginx:alpine
    container_name: art-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - uploads:/var/www/uploads:ro
    depends_on:
      - art-server
    restart: unless-stopped
    networks:
      - art-network

volumes:
  postgres_data:
  redis_data:
  uploads:

networks:
  art-network:
    driver: bridge
