import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseInterceptors,
  UploadedFiles,
  BadRequestException,
  Res,
} from '@nestjs/common';
import { FilesInterceptor } from '@nestjs/platform-express';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiConsumes,
  ApiQuery,
} from '@nestjs/swagger';
import { Response } from 'express';
import { MediaService } from './media.service';
import {
  CreateMediaDto,
  UpdateMediaDto,
  MediaQueryDto,
  MediaUploadDto,
  MediaType,
} from './dto/media.dto';
import { UploadService } from '../upload/upload.service';

@ApiTags('媒体管理')
@Controller('media')
export class MediaController {
  constructor(
    private readonly mediaService: MediaService,
    private readonly uploadService: UploadService,
  ) {}

  @Post('upload')
  @ApiOperation({ summary: '上传媒体文件' })
  @ApiConsumes('multipart/form-data')
  @ApiResponse({ status: 201, description: '上传成功' })
  @UseInterceptors(FilesInterceptor('files', 20))
  async uploadFiles(
    @UploadedFiles() files: Express.Multer.File[],
    @Body() uploadDto: MediaUploadDto,
  ) {
    if (!files || files.length === 0) {
      throw new BadRequestException('请选择要上传的文件');
    }

    const results: Array<{
      success: boolean;
      file?: any;
      fileName?: string;
      error?: string;
    }> = [];

    for (const file of files) {
      try {
        // 使用现有的上传服务上传到OSS
        const uploadResult = await this.uploadService.uploadSingleFile(file);

        // 确定文件类型
        const type = file.mimetype.startsWith('image/')
          ? MediaType.IMAGE
          : MediaType.VIDEO;

        // 创建媒体记录
        const mediaRecord = await this.mediaService.create({
          name: file.originalname,
          originalName: file.originalname,
          type,
          url: uploadResult.fileUrl,
          ossPath: uploadResult.filePath,
          size: file.size,
          mimeType: file.mimetype,
          tags: uploadDto.tags || '',
          category: uploadDto.category,
          description: uploadDto.description,
        });

        results.push({
          success: true,
          file: mediaRecord,
        });
      } catch (error) {
        results.push({
          success: false,
          fileName: file.originalname,
          error: error.message,
        });
      }
    }

    return {
      message: '上传完成',
      results,
      successCount: results.filter((r) => r.success).length,
      failCount: results.filter((r) => !r.success).length,
    };
  }

  @Post()
  @ApiOperation({ summary: '创建媒体记录' })
  @ApiResponse({ status: 201, description: '创建成功' })
  create(@Body() createMediaDto: CreateMediaDto) {
    return this.mediaService.create(createMediaDto);
  }

  @Get()
  @ApiOperation({ summary: '获取媒体文件列表' })
  @ApiQuery({ name: 'search', required: false, description: '搜索关键词' })
  @ApiQuery({
    name: 'type',
    required: false,
    enum: MediaType,
    description: '文件类型',
  })
  @ApiQuery({ name: 'category', required: false, description: '分类' })
  @ApiQuery({ name: 'tag', required: false, description: '标签' })
  @ApiQuery({ name: 'page', required: false, description: '页码' })
  @ApiQuery({ name: 'limit', required: false, description: '每页数量' })
  @ApiResponse({ status: 200, description: '获取成功' })
  findAll(@Query() query: MediaQueryDto) {
    return this.mediaService.findAll(query);
  }

  @Get('statistics')
  @ApiOperation({ summary: '获取媒体统计信息' })
  @ApiResponse({ status: 200, description: '获取成功' })
  getStatistics() {
    return this.mediaService.getStatistics();
  }

  @Get('tags')
  @ApiOperation({ summary: '获取所有标签' })
  @ApiResponse({ status: 200, description: '获取成功' })
  getAllTags() {
    return this.mediaService.getAllTags();
  }

  @Get('categories')
  @ApiOperation({ summary: '获取所有分类' })
  @ApiResponse({ status: 200, description: '获取成功' })
  getAllCategories() {
    return this.mediaService.getAllCategories();
  }

  @Get('by-tags')
  @ApiOperation({ summary: '根据标签获取媒体文件' })
  @ApiQuery({ name: 'tags', description: '标签列表，逗号分隔' })
  @ApiResponse({ status: 200, description: '获取成功' })
  findByTags(@Query('tags') tagsStr: string) {
    const tags = tagsStr.split(',').map((tag) => tag.trim());
    return this.mediaService.findByTags(tags);
  }

  @Get('by-category/:category')
  @ApiOperation({ summary: '根据分类获取媒体文件' })
  @ApiResponse({ status: 200, description: '获取成功' })
  findByCategory(@Param('category') category: string) {
    return this.mediaService.findByCategory(category);
  }

  @Get(':id')
  @ApiOperation({ summary: '获取媒体文件详情' })
  @ApiResponse({ status: 200, description: '获取成功' })
  @ApiResponse({ status: 404, description: '文件不存在' })
  findOne(@Param('id') id: string) {
    return this.mediaService.findOne(id);
  }

  @Get(':id/download')
  @ApiOperation({ summary: '下载媒体文件' })
  @ApiResponse({ status: 200, description: '下载成功' })
  @ApiResponse({ status: 404, description: '文件不存在' })
  async downloadFile(@Param('id') id: string, @Res() res: Response) {
    const media = await this.mediaService.findOne(id);

    // 增加下载次数
    await this.mediaService.incrementDownloadCount(id);

    // 重定向到OSS文件URL
    res.redirect(media.url);
  }

  @Patch(':id')
  @ApiOperation({ summary: '更新媒体文件信息' })
  @ApiResponse({ status: 200, description: '更新成功' })
  @ApiResponse({ status: 404, description: '文件不存在' })
  update(@Param('id') id: string, @Body() updateMediaDto: UpdateMediaDto) {
    return this.mediaService.update(id, updateMediaDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: '删除媒体文件' })
  @ApiResponse({ status: 200, description: '删除成功' })
  @ApiResponse({ status: 404, description: '文件不存在' })
  async remove(@Param('id') id: string) {
    const media = await this.mediaService.findOne(id);

    // 从OSS删除文件
    try {
      await this.uploadService.deleteFile(media.ossPath);
    } catch (error) {
      console.warn('从OSS删除文件失败:', error.message);
    }

    // 删除数据库记录
    return this.mediaService.remove(id);
  }
}
