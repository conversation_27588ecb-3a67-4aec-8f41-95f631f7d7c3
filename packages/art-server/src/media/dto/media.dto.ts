import { ApiProperty, PartialType } from '@nestjs/swagger';
import {
  IsString,
  IsEnum,
  IsOptional,
  IsArray,
  IsInt,
  IsUrl,
  IsJSON,
} from 'class-validator';

export enum MediaType {
  IMAGE = 'IMAGE',
  VIDEO = 'VIDEO',
}

export class CreateMediaDto {
  @ApiProperty({ description: '文件名' })
  @IsString()
  name: string;

  @ApiProperty({ description: '原始文件名', required: false })
  @IsOptional()
  @IsString()
  originalName?: string;

  @ApiProperty({ enum: MediaType, description: '文件类型' })
  @IsEnum(MediaType)
  type: MediaType;

  @ApiProperty({ description: '文件访问URL' })
  @IsUrl()
  url: string;

  @ApiProperty({ description: 'OSS存储路径' })
  @IsString()
  ossPath: string;

  @ApiProperty({ description: '文件大小（字节）' })
  @IsInt()
  size: number;

  @ApiProperty({ description: 'MIME类型' })
  @IsString()
  mimeType: string;

  @ApiProperty({ description: '标签', required: false })
  @IsOptional()
  @IsString()
  tags?: string;

  @ApiProperty({ description: '分类', required: false })
  @IsOptional()
  @IsString()
  category?: string;

  @ApiProperty({ description: '描述', required: false })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ description: '扩展元数据JSON字符串', required: false })
  @IsOptional()
  @IsString()
  metadata?: string;
}

export class UpdateMediaDto extends PartialType(CreateMediaDto) {}

export class MediaQueryDto {
  @ApiProperty({ description: '搜索关键词', required: false })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiProperty({ enum: MediaType, description: '文件类型', required: false })
  @IsOptional()
  @IsEnum(MediaType)
  type?: MediaType;

  @ApiProperty({ description: '分类', required: false })
  @IsOptional()
  @IsString()
  category?: string;

  @ApiProperty({ description: '标签', required: false })
  @IsOptional()
  @IsString()
  tag?: string;

  @ApiProperty({ description: '页码', required: false, default: 1 })
  @IsOptional()
  @IsInt()
  page?: number = 1;

  @ApiProperty({ description: '每页数量', required: false, default: 20 })
  @IsOptional()
  @IsInt()
  limit?: number = 20;
}

export class MediaUploadDto {
  @ApiProperty({ description: '标签', required: false })
  @IsOptional()
  @IsString()
  tags?: string;

  @ApiProperty({ description: '分类', required: false })
  @IsOptional()
  @IsString()
  category?: string;

  @ApiProperty({ description: '描述', required: false })
  @IsOptional()
  @IsString()
  description?: string;
}
