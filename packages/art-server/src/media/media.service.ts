import {
  Injectable,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { PrismaClient, Prisma, $Enums } from '@prisma/client';
import { CreateMediaDto, UpdateMediaDto, MediaQueryDto, MediaType } from './dto/media.dto';

// Media type from Prisma
type Media = {
  id: string;
  name: string;
  originalName: string | null;
  type: $Enums.MediaType;
  url: string;
  ossPath: string;
  size: number;
  mimeType: string;
  tags: string | null;
  category: string | null;
  description: string | null;
  metadata: string | null;
  viewCount: number;
  downloadCount: number;
  createdAt: Date;
  updatedAt: Date;
};

@Injectable()
export class MediaService {
  private prisma = new PrismaClient();

  async create(createMediaDto: CreateMediaDto): Promise<Media> {
    try {
      return await this.prisma.media.create({
        data: createMediaDto,
      });
    } catch (error) {
      throw new BadRequestException('创建媒体记录失败');
    }
  }

  async findAll(query: MediaQueryDto): Promise<{
    data: Media[];
    total: number;
    page: number;
    limit: number;
  }> {
    const { search, type, category, tag, page = 1, limit = 20 } = query;

    const where: Prisma.MediaWhereInput = {};

    // 搜索条件
    if (search) {
      where.OR = [
        { name: { contains: search } },
        { description: { contains: search } },
      ];
    }

    if (type) {
      where.type = type;
    }

    if (category) {
      where.category = category;
    }

    if (tag) {
      where.tags = { contains: tag };
    }

    const skip = (page - 1) * limit;

    const [data, total] = await Promise.all([
      this.prisma.media.findMany({
        where,
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' },
      }),
      this.prisma.media.count({ where }),
    ]);

    return {
      data,
      total,
      page,
      limit,
    };
  }

  async findOne(id: string): Promise<Media> {
    const media = await this.prisma.media.findUnique({
      where: { id },
    });

    if (!media) {
      throw new NotFoundException('媒体文件不存在');
    }

    // 增加浏览次数
    await this.prisma.media.update({
      where: { id },
      data: { viewCount: { increment: 1 } },
    });

    return media;
  }

  async update(id: string, updateMediaDto: UpdateMediaDto): Promise<Media> {
    const existingMedia = await this.prisma.media.findUnique({
      where: { id },
    });

    if (!existingMedia) {
      throw new NotFoundException('媒体文件不存在');
    }

    try {
      return await this.prisma.media.update({
        where: { id },
        data: updateMediaDto,
      });
    } catch (error) {
      throw new BadRequestException('更新媒体记录失败');
    }
  }

  async remove(id: string): Promise<Media> {
    const existingMedia = await this.prisma.media.findUnique({
      where: { id },
    });

    if (!existingMedia) {
      throw new NotFoundException('媒体文件不存在');
    }

    try {
      return await this.prisma.media.delete({
        where: { id },
      });
    } catch (error) {
      throw new BadRequestException('删除媒体记录失败');
    }
  }

  async incrementDownloadCount(id: string): Promise<void> {
    await this.prisma.media.update({
      where: { id },
      data: { downloadCount: { increment: 1 } },
    });
  }

  async getStatistics(): Promise<{
    total: number;
    imageCount: number;
    videoCount: number;
    totalSize: number;
  }> {
    const [total, imageCount, videoCount, totalSizeResult] = await Promise.all([
      this.prisma.media.count(),
      this.prisma.media.count({ where: { type: 'IMAGE' } }),
      this.prisma.media.count({ where: { type: 'VIDEO' } }),
      this.prisma.media.aggregate({
        _sum: { size: true },
      }),
    ]);

    return {
      total,
      imageCount,
      videoCount,
      totalSize: totalSizeResult._sum.size || 0,
    };
  }

  async findByTags(tags: string[]): Promise<Media[]> {
    const tagSearch = tags.join('|');
    return await this.prisma.media.findMany({
      where: {
        OR: tags.map(tag => ({ tags: { contains: tag } })),
      },
      orderBy: { createdAt: 'desc' },
    });
  }

  async findByCategory(category: string): Promise<Media[]> {
    return await this.prisma.media.findMany({
      where: { category },
      orderBy: { createdAt: 'desc' },
    });
  }

  async getAllTags(): Promise<string[]> {
    const result = await this.prisma.media.findMany({
      select: { tags: true },
      where: { tags: { not: null } },
    });

    const allTags = result
      .flatMap((item) => item.tags?.split(',').map(tag => tag.trim()) || [])
      .filter((tag): tag is string => Boolean(tag));
    return [...new Set(allTags)].sort();
  }

  async getAllCategories(): Promise<string[]> {
    const result = await this.prisma.media.findMany({
      select: { category: true },
      where: { category: { not: null } },
      distinct: ['category'],
    });

    return result
      .map((item) => item.category)
      .filter((category): category is string => Boolean(category))
      .sort();
  }

  onModuleDestroy() {
    this.prisma.$disconnect();
  }
}
