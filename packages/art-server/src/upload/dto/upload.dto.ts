export class UploadResponseDto {
  /**
   * 文件ID
   */
  id: string;

  /**
   * 原始文件名
   */
  originalName: string;

  /**
   * 存储的文件名
   */
  fileName: string;

  /**
   * 文件路径
   */
  filePath: string;

  /**
   * 文件URL
   */
  fileUrl: string;

  /**
   * 文件大小（字节）
   */
  size: number;

  /**
   * 文件类型
   */
  mimeType: string;

  /**
   * 文件分类
   */
  fileType: string;

  /**
   * 上传时间
   */
  uploadTime: Date;
}

export class MultipleUploadResponseDto {
  /**
   * 成功上传的文件列表
   */
  success: UploadResponseDto[];

  /**
   * 上传失败的文件列表
   */
  failed: {
    fileName: string;
    error: string;
  }[];

  /**
   * 总文件数
   */
  total: number;

  /**
   * 成功数量
   */
  successCount: number;

  /**
   * 失败数量
   */
  failedCount: number;
}

export class UploadQueryDto {
  /**
   * 页码
   */
  page?: number = 1;

  /**
   * 每页数量
   */
  limit?: number = 10;

  /**
   * 文件类型筛选
   */
  fileType?: string;

  /**
   * 文件名搜索
   */
  search?: string;

  /**
   * 开始时间
   */
  startDate?: string;

  /**
   * 结束时间
   */
  endDate?: string;
}

export class DeleteFileDto {
  /**
   * 要删除的文件ID列表
   */
  fileIds: string[];
}
