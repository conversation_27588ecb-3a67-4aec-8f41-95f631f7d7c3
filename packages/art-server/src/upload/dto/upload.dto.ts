import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class UploadResponseDto {
  @ApiProperty({
    description: '文件ID',
    example: 'file_1703123456789_abc123',
  })
  id: string;

  @ApiProperty({
    description: '原始文件名',
    example: 'example.jpg',
  })
  originalName: string;

  @ApiProperty({
    description: '存储的文件名',
    example: '1703123456789-xyz789.jpg',
  })
  fileName: string;

  @ApiProperty({
    description: '文件路径',
    example: '/uploads/image/1703123456789-xyz789.jpg',
  })
  filePath: string;

  @ApiProperty({
    description: '文件URL',
    example: 'http://localhost:3000/uploads/image/1703123456789-xyz789.jpg',
  })
  fileUrl: string;

  @ApiProperty({
    description: '文件大小（字节）',
    example: 1024000,
  })
  size: number;

  @ApiProperty({
    description: '文件MIME类型',
    example: 'image/jpeg',
  })
  mimeType: string;

  @ApiProperty({
    description: '文件分类',
    example: 'image',
    enum: ['image', 'document', 'archive', 'video', 'audio', 'other'],
  })
  fileType: string;

  @ApiProperty({
    description: '上传时间',
    example: '2023-12-21T10:30:56.789Z',
  })
  uploadTime: Date;
}

export class MultipleUploadResponseDto {
  @ApiProperty({
    description: '成功上传的文件列表',
    type: [UploadResponseDto],
  })
  success: UploadResponseDto[];

  @ApiProperty({
    description: '上传失败的文件列表',
    type: 'array',
    items: {
      type: 'object',
      properties: {
        fileName: { type: 'string', description: '文件名' },
        error: { type: 'string', description: '错误信息' },
      },
    },
    example: [{ fileName: 'error.txt', error: '文件格式不支持' }],
  })
  failed: {
    fileName: string;
    error: string;
  }[];

  @ApiProperty({
    description: '总文件数',
    example: 5,
  })
  total: number;

  @ApiProperty({
    description: '成功数量',
    example: 4,
  })
  successCount: number;

  @ApiProperty({
    description: '失败数量',
    example: 1,
  })
  failedCount: number;
}

export class UploadQueryDto {
  @ApiPropertyOptional({
    description: '页码',
    example: 1,
    default: 1,
    minimum: 1,
  })
  page?: number = 1;

  @ApiPropertyOptional({
    description: '每页数量',
    example: 10,
    default: 10,
    minimum: 1,
    maximum: 100,
  })
  limit?: number = 10;

  @ApiPropertyOptional({
    description: '文件类型筛选',
    example: 'image',
    enum: ['image', 'document', 'archive', 'video', 'audio', 'other'],
  })
  fileType?: string;

  @ApiPropertyOptional({
    description: '文件名搜索',
    example: 'example',
  })
  search?: string;

  @ApiPropertyOptional({
    description: '开始时间',
    example: '2023-12-01',
    format: 'date',
  })
  startDate?: string;

  @ApiPropertyOptional({
    description: '结束时间',
    example: '2023-12-31',
    format: 'date',
  })
  endDate?: string;
}

export class DeleteFileDto {
  @ApiProperty({
    description: '要删除的文件ID列表',
    type: [String],
    example: ['file_1703123456789_abc123', 'file_1703123456790_def456'],
  })
  fileIds: string[];
}
