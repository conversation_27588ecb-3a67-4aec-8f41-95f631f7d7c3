import { Test, TestingModule } from '@nestjs/testing';
import { BadRequestException } from '@nestjs/common';
import { UploadService } from './upload.service';
import * as fs from 'fs/promises';

// Mock fs/promises
jest.mock('fs/promises');
const mockedFs = fs as jest.Mocked<typeof fs>;

describe('UploadService', () => {
  let service: UploadService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [UploadService],
    }).compile();

    service = module.get<UploadService>(UploadService);

    // Reset mocks
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('uploadSingleFile', () => {
    it('should upload a single file successfully', async () => {
      // Mock file object
      const mockFile: Express.Multer.File = {
        fieldname: 'file',
        originalname: 'test.jpg',
        encoding: '7bit',
        mimetype: 'image/jpeg',
        size: 1024,
        destination: '/uploads/image',
        filename: '1703123456789-abc123.jpg',
        path: '/uploads/image/1703123456789-abc123.jpg',
        buffer: Buffer.from('test'),
        stream: null,
      };

      // Mock fs.stat
      mockedFs.stat.mockResolvedValue({
        size: 1024,
      } as any);

      const result = await service.uploadSingleFile(mockFile);

      expect(result).toBeDefined();
      expect(result.originalName).toBe('test.jpg');
      expect(result.fileName).toBe('1703123456789-abc123.jpg');
      expect(result.size).toBe(1024);
      expect(result.mimeType).toBe('image/jpeg');
      expect(result.fileType).toBe('image');
    });

    it('should throw BadRequestException when no file is provided', async () => {
      await expect(service.uploadSingleFile(null)).rejects.toThrow(
        BadRequestException,
      );
    });

    it('should handle file processing errors', async () => {
      const mockFile: Express.Multer.File = {
        fieldname: 'file',
        originalname: 'test.jpg',
        encoding: '7bit',
        mimetype: 'image/jpeg',
        size: 1024,
        destination: '/uploads/image',
        filename: '1703123456789-abc123.jpg',
        path: '/uploads/image/1703123456789-abc123.jpg',
        buffer: Buffer.from('test'),
        stream: null,
      };

      // Mock fs.stat to throw error
      mockedFs.stat.mockRejectedValue(new Error('File not found'));

      // Mock fs.unlink to not throw error when cleaning up
      mockedFs.unlink.mockResolvedValue();

      await expect(service.uploadSingleFile(mockFile)).rejects.toThrow(
        BadRequestException,
      );
    });
  });

  describe('uploadMultipleFiles', () => {
    it('should upload multiple files successfully', async () => {
      const mockFiles: Express.Multer.File[] = [
        {
          fieldname: 'files',
          originalname: 'test1.jpg',
          encoding: '7bit',
          mimetype: 'image/jpeg',
          size: 1024,
          destination: '/uploads/image',
          filename: '1703123456789-abc123.jpg',
          path: '/uploads/image/1703123456789-abc123.jpg',
          buffer: Buffer.from('test1'),
          stream: null,
        },
        {
          fieldname: 'files',
          originalname: 'test2.pdf',
          encoding: '7bit',
          mimetype: 'application/pdf',
          size: 2048,
          destination: '/uploads/document',
          filename: '1703123456790-def456.pdf',
          path: '/uploads/document/1703123456790-def456.pdf',
          buffer: Buffer.from('test2'),
          stream: null,
        },
      ];

      // Mock fs.stat for both files
      mockedFs.stat
        .mockResolvedValueOnce({ size: 1024 } as any)
        .mockResolvedValueOnce({ size: 2048 } as any);

      const result = await service.uploadMultipleFiles(mockFiles);

      expect(result).toBeDefined();
      expect(result.total).toBe(2);
      expect(result.successCount).toBe(2);
      expect(result.failedCount).toBe(0);
      expect(result.success).toHaveLength(2);
      expect(result.failed).toHaveLength(0);
    });

    it('should throw BadRequestException when no files are provided', async () => {
      await expect(service.uploadMultipleFiles([])).rejects.toThrow(
        BadRequestException,
      );
    });

    it('should handle mixed success and failure', async () => {
      const mockFiles: Express.Multer.File[] = [
        {
          fieldname: 'files',
          originalname: 'test1.jpg',
          encoding: '7bit',
          mimetype: 'image/jpeg',
          size: 1024,
          destination: '/uploads/image',
          filename: '1703123456789-abc123.jpg',
          path: '/uploads/image/1703123456789-abc123.jpg',
          buffer: Buffer.from('test1'),
          stream: null,
        },
        {
          fieldname: 'files',
          originalname: 'test2.pdf',
          encoding: '7bit',
          mimetype: 'application/pdf',
          size: 2048,
          destination: '/uploads/document',
          filename: '1703123456790-def456.pdf',
          path: '/uploads/document/1703123456790-def456.pdf',
          buffer: Buffer.from('test2'),
          stream: null,
        },
      ];

      // Mock fs.stat - first succeeds, second fails
      mockedFs.stat
        .mockResolvedValueOnce({ size: 1024 } as any)
        .mockRejectedValueOnce(new Error('File not found'));

      // Mock fs.unlink for cleanup
      mockedFs.unlink.mockResolvedValue();

      const result = await service.uploadMultipleFiles(mockFiles);

      expect(result).toBeDefined();
      expect(result.total).toBe(2);
      expect(result.successCount).toBe(1);
      expect(result.failedCount).toBe(1);
      expect(result.success).toHaveLength(1);
      expect(result.failed).toHaveLength(1);
    });
  });

  describe('deleteFile', () => {
    it('should return success message for file deletion', async () => {
      const result = await service.deleteFile('test-file-id');

      expect(result).toBeDefined();
      expect(result.success).toBe(true);
      expect(result.message).toBe('文件删除成功');
    });
  });

  describe('getFileList', () => {
    it('should return empty file list', async () => {
      const query = { page: 1, limit: 10 };
      const result = await service.getFileList(query);

      expect(result).toBeDefined();
      expect(result.files).toEqual([]);
      expect(result.total).toBe(0);
      expect(result.page).toBe(1);
      expect(result.limit).toBe(10);
    });
  });
});
