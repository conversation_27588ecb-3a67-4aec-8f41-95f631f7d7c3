import {
  Controller,
  Post,
  Get,
  Delete,
  Body,
  Param,
  Query,
  UseInterceptors,
  UploadedFile,
  UploadedFiles,
  BadRequestException,
  HttpStatus,
  HttpCode,
} from '@nestjs/common';
import { FileInterceptor, FilesInterceptor } from '@nestjs/platform-express';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiConsumes,
  ApiBody,
} from '@nestjs/swagger';
import { UploadService } from './upload.service';
import {
  UploadResponseDto,
  MultipleUploadResponseDto,
  UploadQueryDto,
  DeleteFileDto,
} from './dto/upload.dto';
import { multerConfig, getMulterConfigByType } from './upload.config';

@ApiTags('upload')
@Controller('upload')
export class UploadController {
  constructor(private readonly uploadService: UploadService) {}

  /**
   * 单文件上传
   */
  @ApiOperation({ summary: '单文件上传', description: '上传单个文件到服务器' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    description: '文件上传',
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
          description: '要上传的文件',
        },
      },
    },
  })
  @ApiResponse({
    status: 200,
    description: '文件上传成功',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: '文件上传成功' },
        data: { $ref: '#/components/schemas/UploadResponseDto' },
      },
    },
  })
  @ApiResponse({ status: 400, description: '请求参数错误' })
  @Post('single')
  @HttpCode(HttpStatus.OK)
  @UseInterceptors(FileInterceptor('file', multerConfig))
  async uploadSingle(@UploadedFile() file: Express.Multer.File): Promise<{
    success: boolean;
    message: string;
    data: UploadResponseDto;
  }> {
    try {
      const result = await this.uploadService.uploadSingleFile(file);
      return {
        success: true,
        message: '文件上传成功',
        data: result,
      };
    } catch (error) {
      throw new BadRequestException(
        error instanceof Error ? error.message : 'Unknown error',
      );
    }
  }

  /**
   * 多文件上传
   */
  @ApiOperation({
    summary: '多文件上传',
    description: '一次上传多个文件（最多10个）',
  })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    description: '多文件上传',
    schema: {
      type: 'object',
      properties: {
        files: {
          type: 'array',
          items: {
            type: 'string',
            format: 'binary',
          },
          description: '要上传的文件列表',
        },
      },
    },
  })
  @ApiResponse({ status: 200, description: '文件上传完成' })
  @ApiResponse({ status: 400, description: '请求参数错误' })
  @Post('multiple')
  @HttpCode(HttpStatus.OK)
  @UseInterceptors(FilesInterceptor('files', 10, multerConfig))
  async uploadMultiple(@UploadedFiles() files: Express.Multer.File[]): Promise<{
    success: boolean;
    message: string;
    data: MultipleUploadResponseDto;
  }> {
    try {
      const result = await this.uploadService.uploadMultipleFiles(files);
      return {
        success: true,
        message: '文件上传完成',
        data: result,
      };
    } catch (error) {
      throw new BadRequestException(
        error instanceof Error ? error.message : 'Unknown error',
      );
    }
  }

  /**
   * 图片上传（专用接口）
   */
  @ApiOperation({
    summary: '图片上传',
    description: '专用于图片文件上传的接口',
  })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    description: '图片文件上传',
    schema: {
      type: 'object',
      properties: {
        image: {
          type: 'string',
          format: 'binary',
          description: '图片文件（支持jpg, jpeg, png, gif, webp, svg）',
        },
      },
    },
  })
  @ApiResponse({ status: 200, description: '图片上传成功' })
  @ApiResponse({ status: 400, description: '文件格式不支持或请求参数错误' })
  @Post('image')
  @HttpCode(HttpStatus.OK)
  @UseInterceptors(FileInterceptor('image', getMulterConfigByType('IMAGE')))
  async uploadImage(@UploadedFile() file: Express.Multer.File): Promise<{
    success: boolean;
    message: string;
    data: UploadResponseDto;
  }> {
    try {
      const result = await this.uploadService.uploadSingleFile(file);
      return {
        success: true,
        message: '图片上传成功',
        data: result,
      };
    } catch (error) {
      throw new BadRequestException(
        error instanceof Error ? error.message : 'Unknown error',
      );
    }
  }

  /**
   * 文档上传（专用接口）
   */
  @Post('document')
  @HttpCode(HttpStatus.OK)
  @UseInterceptors(
    FileInterceptor('document', getMulterConfigByType('DOCUMENT')),
  )
  async uploadDocument(@UploadedFile() file: Express.Multer.File): Promise<{
    success: boolean;
    message: string;
    data: UploadResponseDto;
  }> {
    try {
      const result = await this.uploadService.uploadSingleFile(file);
      return {
        success: true,
        message: '文档上传成功',
        data: result,
      };
    } catch (error) {
      throw new BadRequestException(
        error instanceof Error ? error.message : 'Unknown error',
      );
    }
  }

  /**
   * 获取文件列表
   */
  @Get('list')
  async getFileList(@Query() query: UploadQueryDto): Promise<{
    success: boolean;
    message: string;
    data: {
      files: UploadResponseDto[];
      total: number;
      page: number;
      limit: number;
    };
  }> {
    try {
      const result = await this.uploadService.getFileList(query);
      return {
        success: true,
        message: '获取文件列表成功',
        data: result,
      };
    } catch (error) {
      throw new BadRequestException(
        error instanceof Error ? error.message : 'Unknown error',
      );
    }
  }

  /**
   * 获取文件信息
   */
  @Get(':fileId')
  async getFileInfo(@Param('fileId') fileId: string): Promise<{
    success: boolean;
    message: string;
    data: UploadResponseDto;
  }> {
    try {
      const result = await this.uploadService.getFileInfo(fileId);
      return {
        success: true,
        message: '获取文件信息成功',
        data: result,
      };
    } catch (error) {
      throw new BadRequestException(
        error instanceof Error ? error.message : 'Unknown error',
      );
    }
  }

  /**
   * 删除单个文件
   */
  @Delete(':fileId')
  async deleteFile(@Param('fileId') fileId: string): Promise<{
    success: boolean;
    message: string;
  }> {
    try {
      const result = await this.uploadService.deleteFile(fileId);
      return result;
    } catch (error) {
      throw new BadRequestException(
        error instanceof Error ? error.message : 'Unknown error',
      );
    }
  }

  /**
   * 批量删除文件
   */
  @Delete()
  async deleteMultipleFiles(@Body() deleteFileDto: DeleteFileDto): Promise<{
    success: boolean;
    message: string;
    data: {
      success: string[];
      failed: { fileId: string; error: string }[];
    };
  }> {
    try {
      const result = await this.uploadService.deleteMultipleFiles(
        deleteFileDto.fileIds,
      );
      return {
        success: true,
        message: '批量删除完成',
        data: result,
      };
    } catch (error) {
      throw new BadRequestException(
        error instanceof Error ? error.message : 'Unknown error',
      );
    }
  }
}
