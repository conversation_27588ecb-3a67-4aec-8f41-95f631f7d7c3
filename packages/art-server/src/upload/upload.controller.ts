import {
  Controller,
  Post,
  Get,
  Delete,
  Body,
  Param,
  Query,
  UseInterceptors,
  UploadedFile,
  UploadedFiles,
  BadRequestException,
  HttpStatus,
  HttpCode,
} from '@nestjs/common';
import { FileInterceptor, FilesInterceptor } from '@nestjs/platform-express';
import { UploadService } from './upload.service';
import {
  UploadResponseDto,
  MultipleUploadResponseDto,
  UploadQueryDto,
  DeleteFileDto,
} from './dto/upload.dto';
import { multerConfig, getMulterConfigByType } from './upload.config';

@Controller('upload')
export class UploadController {
  constructor(private readonly uploadService: UploadService) {}

  /**
   * 单文件上传
   */
  @Post('single')
  @HttpCode(HttpStatus.OK)
  @UseInterceptors(FileInterceptor('file', multerConfig))
  async uploadSingle(@UploadedFile() file: Express.Multer.File): Promise<{
    success: boolean;
    message: string;
    data: UploadResponseDto;
  }> {
    try {
      const result = await this.uploadService.uploadSingleFile(file);
      return {
        success: true,
        message: '文件上传成功',
        data: result,
      };
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  /**
   * 多文件上传
   */
  @Post('multiple')
  @HttpCode(HttpStatus.OK)
  @UseInterceptors(FilesInterceptor('files', 10, multerConfig))
  async uploadMultiple(@UploadedFiles() files: Express.Multer.File[]): Promise<{
    success: boolean;
    message: string;
    data: MultipleUploadResponseDto;
  }> {
    try {
      const result = await this.uploadService.uploadMultipleFiles(files);
      return {
        success: true,
        message: '文件上传完成',
        data: result,
      };
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  /**
   * 图片上传（专用接口）
   */
  @Post('image')
  @HttpCode(HttpStatus.OK)
  @UseInterceptors(FileInterceptor('image', getMulterConfigByType('IMAGE')))
  async uploadImage(@UploadedFile() file: Express.Multer.File): Promise<{
    success: boolean;
    message: string;
    data: UploadResponseDto;
  }> {
    try {
      const result = await this.uploadService.uploadSingleFile(file);
      return {
        success: true,
        message: '图片上传成功',
        data: result,
      };
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  /**
   * 文档上传（专用接口）
   */
  @Post('document')
  @HttpCode(HttpStatus.OK)
  @UseInterceptors(
    FileInterceptor('document', getMulterConfigByType('DOCUMENT')),
  )
  async uploadDocument(@UploadedFile() file: Express.Multer.File): Promise<{
    success: boolean;
    message: string;
    data: UploadResponseDto;
  }> {
    try {
      const result = await this.uploadService.uploadSingleFile(file);
      return {
        success: true,
        message: '文档上传成功',
        data: result,
      };
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  /**
   * 获取文件列表
   */
  @Get('list')
  async getFileList(@Query() query: UploadQueryDto): Promise<{
    success: boolean;
    message: string;
    data: {
      files: UploadResponseDto[];
      total: number;
      page: number;
      limit: number;
    };
  }> {
    try {
      const result = await this.uploadService.getFileList(query);
      return {
        success: true,
        message: '获取文件列表成功',
        data: result,
      };
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  /**
   * 获取文件信息
   */
  @Get(':fileId')
  async getFileInfo(@Param('fileId') fileId: string): Promise<{
    success: boolean;
    message: string;
    data: UploadResponseDto;
  }> {
    try {
      const result = await this.uploadService.getFileInfo(fileId);
      return {
        success: true,
        message: '获取文件信息成功',
        data: result,
      };
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  /**
   * 删除单个文件
   */
  @Delete(':fileId')
  async deleteFile(@Param('fileId') fileId: string): Promise<{
    success: boolean;
    message: string;
  }> {
    try {
      const result = await this.uploadService.deleteFile(fileId);
      return result;
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  /**
   * 批量删除文件
   */
  @Delete()
  async deleteMultipleFiles(@Body() deleteFileDto: DeleteFileDto): Promise<{
    success: boolean;
    message: string;
    data: {
      success: string[];
      failed: { fileId: string; error: string }[];
    };
  }> {
    try {
      const result = await this.uploadService.deleteMultipleFiles(
        deleteFileDto.fileIds,
      );
      return {
        success: true,
        message: '批量删除完成',
        data: result,
      };
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }
}
