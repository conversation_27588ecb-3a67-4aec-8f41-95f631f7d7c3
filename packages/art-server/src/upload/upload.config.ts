import { MulterOptions } from '@nestjs/platform-express/multer/interfaces/multer-options.interface';
import { diskStorage } from 'multer';
import { extname, join } from 'path';
import { existsSync, mkdirSync } from 'fs';

// 支持的文件类型
export const ALLOWED_FILE_TYPES = {
  IMAGE: ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'],
  DOCUMENT: ['pdf', 'doc', 'docx', 'txt', 'rtf'],
  ARCHIVE: ['zip', 'rar', '7z', 'tar', 'gz'],
  VIDEO: ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm'],
  AUDIO: ['mp3', 'wav', 'flac', 'aac', 'ogg'],
};

// 所有允许的文件扩展名
export const ALL_ALLOWED_EXTENSIONS = [
  ...ALLOWED_FILE_TYPES.IMAGE,
  ...ALLOWED_FILE_TYPES.DOCUMENT,
  ...ALLOWED_FILE_TYPES.ARCHIVE,
  ...ALLOWED_FILE_TYPES.VIDEO,
  ...ALLOWED_FILE_TYPES.AUDIO,
];

// 文件大小限制 (字节)
export const FILE_SIZE_LIMITS = {
  IMAGE: 5 * 1024 * 1024, // 5MB
  DOCUMENT: 10 * 1024 * 1024, // 10MB
  ARCHIVE: 50 * 1024 * 1024, // 50MB
  VIDEO: 100 * 1024 * 1024, // 100MB
  AUDIO: 20 * 1024 * 1024, // 20MB
  DEFAULT: 10 * 1024 * 1024, // 10MB
};

// 生成唯一文件名
export const generateFileName = (originalName: string): string => {
  const timestamp = Date.now();
  const randomString = Math.random().toString(36).substring(2, 15);
  const extension = extname(originalName);
  return `${timestamp}-${randomString}${extension}`;
};

// 获取文件类型
export const getFileType = (extension: string): string => {
  const ext = extension.toLowerCase().replace('.', '');
  
  if (ALLOWED_FILE_TYPES.IMAGE.includes(ext)) return 'image';
  if (ALLOWED_FILE_TYPES.DOCUMENT.includes(ext)) return 'document';
  if (ALLOWED_FILE_TYPES.ARCHIVE.includes(ext)) return 'archive';
  if (ALLOWED_FILE_TYPES.VIDEO.includes(ext)) return 'video';
  if (ALLOWED_FILE_TYPES.AUDIO.includes(ext)) return 'audio';
  
  return 'other';
};

// 创建上传目录
export const createUploadDir = (subDir: string = ''): string => {
  const uploadPath = join(process.cwd(), 'uploads', subDir);
  
  if (!existsSync(uploadPath)) {
    mkdirSync(uploadPath, { recursive: true });
  }
  
  return uploadPath;
};

// Multer配置
export const multerConfig: MulterOptions = {
  storage: diskStorage({
    destination: (req, file, callback) => {
      const fileType = getFileType(extname(file.originalname));
      const uploadPath = createUploadDir(fileType);
      callback(null, uploadPath);
    },
    filename: (req, file, callback) => {
      const fileName = generateFileName(file.originalname);
      callback(null, fileName);
    },
  }),
  fileFilter: (req, file, callback) => {
    const extension = extname(file.originalname).toLowerCase().replace('.', '');
    
    if (ALL_ALLOWED_EXTENSIONS.includes(extension)) {
      callback(null, true);
    } else {
      callback(new Error(`不支持的文件类型: ${extension}`), false);
    }
  },
  limits: {
    fileSize: FILE_SIZE_LIMITS.DEFAULT,
  },
};

// 根据文件类型获取特定配置
export const getMulterConfigByType = (fileType: keyof typeof ALLOWED_FILE_TYPES): MulterOptions => {
  return {
    ...multerConfig,
    limits: {
      fileSize: FILE_SIZE_LIMITS[fileType] || FILE_SIZE_LIMITS.DEFAULT,
    },
    fileFilter: (req, file, callback) => {
      const extension = extname(file.originalname).toLowerCase().replace('.', '');
      
      if (ALLOWED_FILE_TYPES[fileType].includes(extension)) {
        callback(null, true);
      } else {
        callback(new Error(`不支持的${fileType.toLowerCase()}文件类型: ${extension}`), false);
      }
    },
  };
};
