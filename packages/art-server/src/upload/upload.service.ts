import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { extname, join } from 'path';
import { unlink, stat } from 'fs/promises';
import { existsSync } from 'fs';
import { 
  UploadResponseDto, 
  MultipleUploadResponseDto, 
  UploadQueryDto 
} from './dto/upload.dto';
import { getFileType, generateFileName } from './upload.config';

@Injectable()
export class UploadService {
  private readonly baseUrl = process.env.BASE_URL || 'http://localhost:3000';
  private readonly uploadPath = join(process.cwd(), 'uploads');

  /**
   * 处理单文件上传
   */
  async uploadSingleFile(file: Express.Multer.File): Promise<UploadResponseDto> {
    if (!file) {
      throw new BadRequestException('没有上传文件');
    }

    try {
      const fileStats = await stat(file.path);
      const fileType = getFileType(extname(file.originalname));
      
      const response: UploadResponseDto = {
        id: this.generateFileId(),
        originalName: file.originalname,
        fileName: file.filename,
        filePath: file.path,
        fileUrl: this.generateFileUrl(file.path),
        size: fileStats.size,
        mimeType: file.mimetype,
        fileType,
        uploadTime: new Date(),
      };

      return response;
    } catch (error) {
      // 如果处理失败，删除已上传的文件
      await this.deletePhysicalFile(file.path);
      throw new BadRequestException(`文件处理失败: ${error.message}`);
    }
  }

  /**
   * 处理多文件上传
   */
  async uploadMultipleFiles(files: Express.Multer.File[]): Promise<MultipleUploadResponseDto> {
    if (!files || files.length === 0) {
      throw new BadRequestException('没有上传文件');
    }

    const success: UploadResponseDto[] = [];
    const failed: { fileName: string; error: string }[] = [];

    for (const file of files) {
      try {
        const result = await this.uploadSingleFile(file);
        success.push(result);
      } catch (error) {
        failed.push({
          fileName: file.originalname,
          error: error.message,
        });
      }
    }

    return {
      success,
      failed,
      total: files.length,
      successCount: success.length,
      failedCount: failed.length,
    };
  }

  /**
   * 获取文件列表
   */
  async getFileList(query: UploadQueryDto): Promise<{
    files: UploadResponseDto[];
    total: number;
    page: number;
    limit: number;
  }> {
    // 这里应该从数据库获取文件列表
    // 由于没有数据库配置，这里返回模拟数据
    // 在实际项目中，你需要实现数据库查询逻辑
    
    const mockFiles: UploadResponseDto[] = [];
    
    return {
      files: mockFiles,
      total: mockFiles.length,
      page: query.page || 1,
      limit: query.limit || 10,
    };
  }

  /**
   * 删除文件
   */
  async deleteFile(fileId: string): Promise<{ success: boolean; message: string }> {
    try {
      // 这里应该从数据库查找文件信息
      // 由于没有数据库配置，这里返回模拟逻辑
      
      // const fileRecord = await this.findFileById(fileId);
      // if (!fileRecord) {
      //   throw new NotFoundException('文件不存在');
      // }
      
      // await this.deletePhysicalFile(fileRecord.filePath);
      // await this.deleteFileRecord(fileId);
      
      return {
        success: true,
        message: '文件删除成功',
      };
    } catch (error) {
      throw new BadRequestException(`删除文件失败: ${error.message}`);
    }
  }

  /**
   * 批量删除文件
   */
  async deleteMultipleFiles(fileIds: string[]): Promise<{
    success: string[];
    failed: { fileId: string; error: string }[];
  }> {
    const success: string[] = [];
    const failed: { fileId: string; error: string }[] = [];

    for (const fileId of fileIds) {
      try {
        await this.deleteFile(fileId);
        success.push(fileId);
      } catch (error) {
        failed.push({
          fileId,
          error: error.message,
        });
      }
    }

    return { success, failed };
  }

  /**
   * 获取文件信息
   */
  async getFileInfo(fileId: string): Promise<UploadResponseDto> {
    // 这里应该从数据库查找文件信息
    // 由于没有数据库配置，这里抛出异常
    throw new NotFoundException('文件不存在');
  }

  /**
   * 生成文件ID
   */
  private generateFileId(): string {
    return `file_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;
  }

  /**
   * 生成文件访问URL
   */
  private generateFileUrl(filePath: string): string {
    const relativePath = filePath.replace(this.uploadPath, '').replace(/\\/g, '/');
    return `${this.baseUrl}/uploads${relativePath}`;
  }

  /**
   * 删除物理文件
   */
  private async deletePhysicalFile(filePath: string): Promise<void> {
    try {
      if (existsSync(filePath)) {
        await unlink(filePath);
      }
    } catch (error) {
      console.error(`删除文件失败: ${filePath}`, error);
    }
  }
}
