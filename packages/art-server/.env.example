# 应用配置
NODE_ENV=development
PORT=3000
BASE_URL=http://localhost:3000

# 数据库配置
DATABASE_URL=postgresql://postgres:password@localhost:5432/artdb?schema=public

# Redis配置
REDIS_URL=redis://localhost:6379

# JWT配置
JWT_SECRET=your-super-secret-jwt-key
JWT_EXPIRES_IN=7d

# 文件上传配置
UPLOAD_MAX_SIZE=100MB
UPLOAD_ALLOWED_TYPES=jpg,jpeg,png,gif,webp,svg,pdf,doc,docx,txt,zip,rar

# 邮件配置（可选）
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# 第三方服务配置（可选）
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=us-east-1
AWS_S3_BUCKET=your-s3-bucket

# 监控配置（可选）
SENTRY_DSN=your-sentry-dsn

# 日志配置
LOG_LEVEL=info
LOG_FORMAT=json

# CORS配置
CORS_ORIGIN=http://localhost:3000,http://localhost:3001

# 限流配置
RATE_LIMIT_TTL=60
RATE_LIMIT_LIMIT=100

# 缓存配置
CACHE_TTL=3600
