# 开发环境Dockerfile
FROM node:18-alpine

# 设置工作目录
WORKDIR /app

# 安装pnpm
RUN npm install -g pnpm

# 创建非root用户
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nestjs -u 1001

# 复制package.json和pnpm-lock.yaml
COPY package.json pnpm-lock.yaml* ./

# 安装所有依赖（包括开发依赖）
RUN pnpm install --frozen-lockfile

# 创建uploads目录并设置权限
RUN mkdir -p uploads public && \
    chown -R nestjs:nodejs uploads public

# 切换到非root用户
USER nestjs

# 暴露端口
EXPOSE 3000 9229

# 设置环境变量
ENV NODE_ENV=development
ENV PORT=3000

# 启动开发服务器
CMD ["pnpm", "run", "start:dev"]
