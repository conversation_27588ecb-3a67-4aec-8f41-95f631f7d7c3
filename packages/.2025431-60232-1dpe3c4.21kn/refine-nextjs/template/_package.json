{
    "name": "<%= name %>",
    "version": "0.1.0",
    "private": true,
    "engines": {
        "node": ">=18.0.0"
    },
    "scripts": {
        "dev": "cross-env NODE_OPTIONS=--max_old_space_size=4096 refine dev",
        "build": "refine build",
        "start": "refine start",
        "lint": "next lint",
        "refine": "refine"
    },
    "dependencies": {
        "@refinedev/cli": "^2.16.21",
        "@refinedev/core": "^4.47.1",
        "@refinedev/devtools": "^1.1.32",
        "@refinedev/nextjs-router": "^6.0.0",
        "@refinedev/kbar": "^1.3.6",
        "next": "^14.1.0",
        "react": "^18.0.0",
        "react-dom": "^18.0.0"
    },
    "devDependencies": {
        "@types/react": "^18.0.0",
        "@types/react-dom": "^18.0.0",
        "@types/node": "^18.16.2",
        "@typescript-eslint/parser": "^6.21.0",
        "typescript": "^5.4.2",
        "cross-env": "^7.0.3",    
        "eslint": "^8",
        "eslint-config-next": "^14.1.0"
    <%_ if (typeof projectId !== 'undefined' && projectId !== '') { _%>
    },
    "refine": {
        "projectId": "<%= projectId %>"
    }
    <%_ } else { _%>
    }
    <%_ } _%>
}
