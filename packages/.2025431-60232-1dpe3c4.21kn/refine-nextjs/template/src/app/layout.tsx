import { Metadata } from "next";
<%- (_app.nextjsImport || []).join("\n") _%>
import React, { Suspense } from 'react'
import { Refine, GitHubBanner, <%- (_app.refineImports || []).join("\n,") _%> } from '@refinedev/core';
import { DevtoolsProvider } from '@providers/devtools'
import { RefineKbar, RefineKbarProvider } from "@refinedev/kbar";
<%_ if (answers["ui-framework"] === 'antd') { _%>
    import { <%- (_app.refineAntdImports || []).join("\n,") _%> } from '@refinedev/antd';
<%_ } _%>
<%_ if (answers["ui-framework"] === 'mui') { _%>
    import { <%- (_app.refineMuiImports || []).join("\n,") _%> } from '@refinedev/mui';
<%_ } _%>
import routerProvider from "@refinedev/nextjs-router";

<%- (_app.import || []).join("\n") _%>

<%- (_app.localImport || []).join("\n") _%>

<%- (_app.relativeImport || []).join("\n") _%>

<%- (_app.afterImport || []).join("\n") _%>

<%
    var top = _app.wrapper.map(wrapper => wrapper[0] || "");
    var bottom = _app.wrapper.map(wrapper => wrapper[1] || "").reverse();
%>

export const metadata: Metadata = {
    title: "Refine",
    description: "Generated by create refine app",
    icons: {
        icon: "/favicon.ico",
    },
};

export default function RootLayout({
    children,
}: Readonly<{
    children: React.ReactNode;
}>) {

    <%- (_app.innerHooks || []).join("\n") %>
    <%- (_app.inner || []).join("\n") %>
    <%- (_app.nextjsInner || []).join("\n") %>

    return (
        <html lang="en">
            <body>
            <Suspense>
                <GitHubBanner />
                <RefineKbarProvider>
                <%- top.join("\n") %>
                <DevtoolsProvider>
                    <Refine 
                        routerProvider={routerProvider}
                        <%- (_app.refineProps || []).join("\n") %>
                        <%_ if (_app.hasRoutes === true) { _%>
                        resources={[
                            <%_ if (answers["data-provider"] === 'data-provider-strapi-v4') { _%>
                            {
                                name: "blog-posts",
                                list: "/blog-posts",
                                create: "/blog-posts/create",
                                edit: "/blog-posts/edit/:id",
                                show: "/blog-posts/show/:id",
                                meta: {
                                    canDelete: true,
                                },
                            },
                            {
                                name: "categories",
                                list: "/categories",
                                create: "/categories/create",
                                edit: "/categories/edit/:id",
                                show: "/categories/show/:id",
                                meta: {
                                    canDelete: true,
                                },
                            }
                            <%_ } else { _%>
                            {
                                name: "blog_posts",
                                list: "/blog-posts",
                                create: "/blog-posts/create",
                                edit: "/blog-posts/edit/:id",
                                show: "/blog-posts/show/:id",
                                meta: {
                                    canDelete: true,
                                },
                            },
                            {
                                name: "categories",
                                list: "/categories",
                                create: "/categories/create",
                                edit: "/categories/edit/:id",
                                show: "/categories/show/:id",
                                meta: {
                                    canDelete: true,
                                },
                            }
                            <%_ } _%>
                        ]}
                        <%_ } _%>
                        options={{
                            syncWithLocation: true,
                            warnWhenUnsavedChanges: true,
                            useNewQueryKeys: true,
                            <%_ if (typeof projectId !== 'undefined' && projectId !== '') { _%>
                                projectId: "<%= projectId %>",
                            <%_ } _%>
                            <%- (_app.refineOptions || []).join("\n") %> 
                        }}
                    >
                        {children}
                        <RefineKbar />
                    </Refine>
                </DevtoolsProvider>
                <%- bottom.join("\n") %>
                </RefineKbarProvider>
            </Suspense>
            </body>
        </html>
    );
}
