'use client'

import React, {
    type PropsWithChildren,
    createContext,
    useEffect,
    useState,
} from "react";
import { RefineThemes } from "@refinedev/antd";
import { ConfigProvider, App as AntdApp, theme } from 'antd'
import Cookies from 'js-cookie'

type ColorModeContextType = {
    mode: string;
    setMode: (mode: string) => void;
};

export const ColorModeContext = createContext<ColorModeContextType>(
    {} as ColorModeContextType,
);

type ColorModeContextProviderProps = {
    defaultMode?: string;
};

export const ColorModeContextProvider: React.FC<
    PropsWithChildren<ColorModeContextProviderProps>
> = ({ children, defaultMode }) => {
    const [isMounted, setIsMounted] = useState(false);
    const [mode, setMode] = useState(defaultMode || "light");

    useEffect(() => {
        setIsMounted(true);
    }, []);

    useEffect(() => {
        if (isMounted) {
            const theme = Cookies.get('theme') || 'light'
            setMode(theme)
        }
    }, [isMounted]);

    const setColorMode = () => {
        if (mode === "light") {
            setMode("dark");
            Cookies.set('theme', 'dark')
        } else {
            setMode("light");
            Cookies.set('theme', 'light')
        }
    };

    const { darkAlgorithm, defaultAlgorithm } = theme;

    return (
        <ColorModeContext.Provider
            value={{
                setMode: setColorMode,
                mode,
            }}
        >
            <ConfigProvider
                // you can change the theme colors here. example: ...RefineThemes.Magenta,
                theme={{
                    ...RefineThemes.<%= selectedTheme %>,
                    algorithm:
                        mode === "light" ? defaultAlgorithm : darkAlgorithm,
                }}
            >
                <AntdApp>{children}</AntdApp>
            </ConfigProvider>
        </ColorModeContext.Provider>
    );
};
