"use client";

import type { AuthProvider } from "@refinedev/core";
import Cookies from "js-cookie";

const mockUsers = [
    {
        name: "<PERSON>",
        email: "johndo<PERSON>@mail.com",
        roles: ["admin"],
        avatar: "https://i.pravatar.cc/150?img=1",
    },
    {
        name: "<PERSON>",
        email: "jane<PERSON><EMAIL>",
        roles: ["editor"],
        avatar: "https://i.pravatar.cc/150?img=1",
    },
];

export const authProviderClient: AuthProvider = {
    login: async ({ email, username, password, remember }) => {
        // Suppose we actually send a request to the back end here.
        const user = mockUsers[0];

        if (user) {
            Cookies.set("auth", JSON.stringify(user), {
                expires: 30, // 30 days
                path: "/",
            });
            return {
                success: true,
                redirectTo: "/",
            };
        }

        return {
            success: false,
            error: {
                name: "<PERSON><PERSON><PERSON><PERSON><PERSON>",
                message: "Invalid username or password",
            },
        };
    },
    logout: async () => {
        Cookies.remove("auth", { path: "/" });
        return {
            success: true,
            redirectTo: "/login",
        };
    },
    check: async () => {
        const auth = Cookies.get("auth");
        if (auth) {
            return {
                authenticated: true,
            };
        }

        return {
            authenticated: false,
            logout: true,
            redirectTo: "/login",
        };
    },
    getPermissions: async () => {
        const auth = Cookies.get("auth");
        if (auth) {
            const parsedUser = JSON.parse(auth);
            return parsedUser.roles;
        }
        return null;
    },
    getIdentity: async () => {
        const auth = Cookies.get("auth");
        if (auth) {
            const parsedUser = JSON.parse(auth);
            return parsedUser;
        }
        return null;
    },
    onError: async (error) => {
        if (error.response?.status === 401) {
            return {
                logout: true,
            };
        }

        return { error };
    },
};
