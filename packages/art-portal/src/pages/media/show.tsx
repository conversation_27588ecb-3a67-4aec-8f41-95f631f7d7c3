import { useState } from "react";
import { Show, useShow } from "@refinedev/antd";
import {
  Card,
  Space,
  Typography,
  Divider,
  Image,
  Tag,
  Button,
  Row,
  Col,
  Descriptions,
  Modal,
  message,
} from "antd";
import {
  FileImageOutlined,
  VideoCameraOutlined,
  DownloadOutlined,
  CopyOutlined,
  EditOutlined,
} from "@ant-design/icons";
import { MediaRecord } from "./list";

const { Title, Text, Paragraph } = Typography;

export const MediaShow = () => {
  const { queryResult } = useShow<MediaRecord>();
  const { data, isLoading } = queryResult;
  const record = data?.data;

  const [previewVisible, setPreviewVisible] = useState(false);

  // 格式化文件大小
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return "0 B";
    const k = 1024;
    const sizes = ["B", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  // 复制链接
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text).then(() => {
      message.success("链接已复制到剪贴板");
    });
  };

  // 下载文件
  const handleDownload = () => {
    if (record) {
      const link = document.createElement("a");
      link.href = record.url;
      link.download = record.name;
      link.click();
    }
  };

  if (!record) {
    return <div>加载中...</div>;
  }

  return (
    <Show isLoading={isLoading}>
      <Row gutter={24}>
        {/* 左侧：文件预览 */}
        <Col xs={24} lg={12}>
          <Card title="文件预览">
            <div style={{ textAlign: "center", padding: 24 }}>
              {record.type === "image" ? (
                <Image
                  src={record.url}
                  alt={record.name}
                  style={{ maxWidth: "100%", maxHeight: 400 }}
                  onClick={() => setPreviewVisible(true)}
                />
              ) : (
                <div style={{ position: "relative" }}>
                  <video
                    src={record.url}
                    controls
                    style={{ maxWidth: "100%", maxHeight: 400 }}
                  />
                </div>
              )}
            </div>

            <Divider />

            {/* 操作按钮 */}
            <Space style={{ width: "100%", justifyContent: "center" }}>
              <Button
                type="primary"
                icon={<DownloadOutlined />}
                onClick={handleDownload}
              >
                下载文件
              </Button>
              <Button
                icon={<CopyOutlined />}
                onClick={() => copyToClipboard(record.url)}
              >
                复制链接
              </Button>
              <Button icon={<EditOutlined />} href={`/media/edit/${record.id}`}>
                编辑
              </Button>
            </Space>
          </Card>
        </Col>

        {/* 右侧：文件信息 */}
        <Col xs={24} lg={12}>
          <Card title="文件信息">
            <Descriptions column={1} bordered>
              <Descriptions.Item label="文件名">
                <Text copyable>{record.name}</Text>
              </Descriptions.Item>

              <Descriptions.Item label="文件类型">
                <Tag
                  color={record.type === "image" ? "blue" : "green"}
                  icon={
                    record.type === "image" ? (
                      <FileImageOutlined />
                    ) : (
                      <VideoCameraOutlined />
                    )
                  }
                >
                  {record.type === "image" ? "图片" : "视频"}
                </Tag>
              </Descriptions.Item>

              <Descriptions.Item label="文件大小">
                {formatFileSize(record.size)}
              </Descriptions.Item>

              <Descriptions.Item label="上传时间">
                {new Date(record.createdAt).toLocaleString()}
              </Descriptions.Item>

              <Descriptions.Item label="最后修改">
                {new Date(record.updatedAt).toLocaleString()}
              </Descriptions.Item>

              {record.tags && record.tags.length > 0 && (
                <Descriptions.Item label="标签">
                  <Space wrap>
                    {record.tags.map((tag) => (
                      <Tag key={tag} color="default">
                        {tag}
                      </Tag>
                    ))}
                  </Space>
                </Descriptions.Item>
              )}

              {record.description && (
                <Descriptions.Item label="描述">
                  <Paragraph
                    ellipsis={{ rows: 3, expandable: true, symbol: "展开" }}
                  >
                    {record.description}
                  </Paragraph>
                </Descriptions.Item>
              )}

              <Descriptions.Item label="访问链接">
                <Text copyable style={{ fontSize: 12, wordBreak: "break-all" }}>
                  {record.url}
                </Text>
              </Descriptions.Item>

              <Descriptions.Item label="存储路径">
                <Text
                  type="secondary"
                  style={{ fontSize: 12, wordBreak: "break-all" }}
                >
                  {record.ossPath}
                </Text>
              </Descriptions.Item>
            </Descriptions>
          </Card>

          {/* 使用统计 */}
          <Card title="使用统计" style={{ marginTop: 16 }}>
            <Row gutter={16}>
              <Col span={8}>
                <div style={{ textAlign: "center" }}>
                  <Title level={3} style={{ margin: 0 }}>
                    0
                  </Title>
                  <Text type="secondary">引用次数</Text>
                </div>
              </Col>
              <Col span={8}>
                <div style={{ textAlign: "center" }}>
                  <Title level={3} style={{ margin: 0 }}>
                    0
                  </Title>
                  <Text type="secondary">下载次数</Text>
                </div>
              </Col>
              <Col span={8}>
                <div style={{ textAlign: "center" }}>
                  <Title level={3} style={{ margin: 0 }}>
                    0
                  </Title>
                  <Text type="secondary">浏览次数</Text>
                </div>
              </Col>
            </Row>
          </Card>
        </Col>
      </Row>

      {/* 全屏预览模态框 */}
      <Modal
        title="全屏预览"
        open={previewVisible}
        onCancel={() => setPreviewVisible(false)}
        footer={null}
        width="90%"
        style={{ top: 20 }}
      >
        <div style={{ textAlign: "center" }}>
          {record.type === "image" ? (
            <Image
              src={record.url}
              alt={record.name}
              style={{ maxWidth: "100%", maxHeight: "70vh" }}
            />
          ) : (
            <video
              src={record.url}
              controls
              style={{ maxWidth: "100%", maxHeight: "70vh" }}
            />
          )}
        </div>
      </Modal>
    </Show>
  );
};
