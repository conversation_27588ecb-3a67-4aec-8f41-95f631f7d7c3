import { Edit, useForm } from "@refinedev/antd";
import {
  Form,
  Input,
  Select,
  Card,
  Space,
  Typography,
  Divider,
  Image,
  Tag,
} from "antd";
import { FileImageOutlined, VideoCameraOutlined } from "@ant-design/icons";

const { TextArea } = Input;
const { Title, Text } = Typography;
const { Option } = Select;

export const MediaEdit = () => {
  const { formProps, saveButtonProps, queryResult } = useForm();
  const mediaData = queryResult?.data?.data;

  // 格式化文件大小
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return "0 B";
    const k = 1024;
    const sizes = ["B", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  return (
    <Edit saveButtonProps={saveButtonProps}>
      <Card>
        <Title level={4}>编辑媒体文件</Title>
        <Divider />

        {/* 文件预览 */}
        {mediaData && (
          <Card style={{ marginBottom: 24 }}>
            <Space direction="vertical" style={{ width: "100%" }}>
              <Text strong>文件预览</Text>
              <div style={{ textAlign: "center", padding: 16 }}>
                {mediaData.type === "image" ? (
                  <Image
                    src={mediaData.url}
                    alt={mediaData.name}
                    style={{ maxWidth: 300, maxHeight: 200 }}
                  />
                ) : (
                  <div style={{ display: "inline-block" }}>
                    <video
                      src={mediaData.url}
                      controls
                      style={{ maxWidth: 300, maxHeight: 200 }}
                    />
                  </div>
                )}
              </div>

              <Space wrap>
                <Tag
                  color={mediaData.type === "image" ? "blue" : "green"}
                  icon={
                    mediaData.type === "image" ? (
                      <FileImageOutlined />
                    ) : (
                      <VideoCameraOutlined />
                    )
                  }
                >
                  {mediaData.type === "image" ? "图片" : "视频"}
                </Tag>
                <Text type="secondary">
                  大小: {formatFileSize(mediaData.size)}
                </Text>
                <Text type="secondary">
                  上传时间: {new Date(mediaData.createdAt).toLocaleString()}
                </Text>
              </Space>
            </Space>
          </Card>
        )}

        <Form {...formProps} layout="vertical">
          {/* 文件名 */}
          <Form.Item
            label="文件名"
            name="name"
            rules={[
              {
                required: true,
                message: "请输入文件名",
              },
            ]}
          >
            <Input placeholder="输入文件名" />
          </Form.Item>

          {/* 标签 */}
          <Form.Item
            label="标签"
            name="tags"
            help="为文件添加标签，便于分类和搜索"
          >
            <Select
              mode="tags"
              placeholder="输入标签，按回车添加"
              style={{ width: "100%" }}
              tokenSeparators={[","]}
            >
              <Option value="产品图片">产品图片</Option>
              <Option value="宣传视频">宣传视频</Option>
              <Option value="用户头像">用户头像</Option>
              <Option value="背景图">背景图</Option>
              <Option value="教程视频">教程视频</Option>
              <Option value="营销素材">营销素材</Option>
              <Option value="系统资源">系统资源</Option>
            </Select>
          </Form.Item>

          {/* 分类 */}
          <Form.Item label="分类" name="category" help="选择文件分类">
            <Select placeholder="选择分类">
              <Option value="product">产品相关</Option>
              <Option value="marketing">营销素材</Option>
              <Option value="user">用户内容</Option>
              <Option value="system">系统资源</Option>
              <Option value="other">其他</Option>
            </Select>
          </Form.Item>

          {/* 描述 */}
          <Form.Item label="描述" name="description" help="为文件添加描述信息">
            <TextArea
              rows={4}
              placeholder="输入文件描述..."
              maxLength={500}
              showCount
            />
          </Form.Item>

          {/* 文件信息（只读） */}
          <Divider />
          <Title level={5}>文件信息</Title>

          {mediaData && (
            <Space direction="vertical" style={{ width: "100%" }}>
              <div>
                <Text strong>原始文件名: </Text>
                <Text>{mediaData.originalName || mediaData.name}</Text>
              </div>

              <div>
                <Text strong>文件类型: </Text>
                <Text>{mediaData.type}</Text>
              </div>

              <div>
                <Text strong>文件大小: </Text>
                <Text>{formatFileSize(mediaData.size)}</Text>
              </div>

              <div>
                <Text strong>存储路径: </Text>
                <Text type="secondary" style={{ fontSize: 12 }}>
                  {mediaData.ossPath}
                </Text>
              </div>

              <div>
                <Text strong>文件URL: </Text>
                <Text
                  type="secondary"
                  style={{ fontSize: 12, wordBreak: "break-all" }}
                >
                  {mediaData.url}
                </Text>
              </div>
            </Space>
          )}
        </Form>
      </Card>
    </Edit>
  );
};
