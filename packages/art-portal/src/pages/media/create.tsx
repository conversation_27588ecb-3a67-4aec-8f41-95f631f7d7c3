import { useState } from "react";
import { Create, useForm } from "@refinedev/antd";
import {
  Form,
  Input,
  Select,
  Card,
  Space,
  Typography,
  Divider,
  Tag,
  message,
} from "antd";
import { MediaUpload, MediaFile } from "../../components/MediaUpload";

const { TextArea } = Input;
const { Title, Text } = Typography;
const { Option } = Select;

export const MediaCreate = () => {
  const { formProps, saveButtonProps } = useForm();
  const [uploadedFiles, setUploadedFiles] = useState<MediaFile[]>([]);

  // 处理表单提交
  const handleFinish = (values: any) => {
    if (uploadedFiles.length === 0) {
      message.error("请至少上传一个文件");
      return;
    }

    // 将上传的文件信息包含在表单数据中
    const formData = {
      ...values,
      files: uploadedFiles.map((file) => ({
        name: file.name,
        url: file.url,
        type: file.type,
        size: file.size,
        ossPath: file.ossPath,
      })),
    };

    // 这里应该调用API保存数据
    console.log("提交的数据:", formData);
    message.success("上传成功");
  };

  return (
    <Create saveButtonProps={saveButtonProps}>
      <Card>
        <Title level={4}>上传媒体文件</Title>
        <Divider />

        <Form {...formProps} layout="vertical" onFinish={handleFinish}>
          {/* 文件上传区域 */}
          <Form.Item label="文件上传" required>
            <MediaUpload
              value={uploadedFiles}
              onChange={setUploadedFiles}
              maxCount={20}
              multiple={true}
            />
          </Form.Item>

          {uploadedFiles.length > 0 && (
            <>
              <Divider />

              {/* 文件信息 */}
              <Form.Item label="文件信息">
                <Space direction="vertical" style={{ width: "100%" }}>
                  <Text strong>已上传 {uploadedFiles.length} 个文件：</Text>
                  {uploadedFiles.map((file, index) => (
                    <div
                      key={file.id}
                      style={{
                        padding: 8,
                        backgroundColor: "#f5f5f5",
                        borderRadius: 4,
                      }}
                    >
                      <Space>
                        <Tag color={file.type === "image" ? "blue" : "green"}>
                          {file.type === "image" ? "图片" : "视频"}
                        </Tag>
                        <Text ellipsis style={{ maxWidth: 300 }}>
                          {file.name}
                        </Text>
                        <Text type="secondary">
                          ({(file.size / 1024 / 1024).toFixed(2)} MB)
                        </Text>
                      </Space>
                    </div>
                  ))}
                </Space>
              </Form.Item>

              {/* 批量标签 */}
              <Form.Item
                label="标签"
                name="tags"
                help="为上传的文件添加标签，用逗号分隔"
              >
                <Select
                  mode="tags"
                  placeholder="输入标签，按回车添加"
                  style={{ width: "100%" }}
                  tokenSeparators={[","]}
                >
                  <Option value="产品图片">产品图片</Option>
                  <Option value="宣传视频">宣传视频</Option>
                  <Option value="用户头像">用户头像</Option>
                  <Option value="背景图">背景图</Option>
                  <Option value="教程视频">教程视频</Option>
                </Select>
              </Form.Item>

              {/* 批量描述 */}
              <Form.Item
                label="描述"
                name="description"
                help="为这批文件添加描述信息"
              >
                <TextArea
                  rows={3}
                  placeholder="输入文件描述..."
                  maxLength={500}
                  showCount
                />
              </Form.Item>

              {/* 分类 */}
              <Form.Item label="分类" name="category" help="选择文件分类">
                <Select placeholder="选择分类">
                  <Option value="product">产品相关</Option>
                  <Option value="marketing">营销素材</Option>
                  <Option value="user">用户内容</Option>
                  <Option value="system">系统资源</Option>
                  <Option value="other">其他</Option>
                </Select>
              </Form.Item>
            </>
          )}
        </Form>
      </Card>
    </Create>
  );
};
